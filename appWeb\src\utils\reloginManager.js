/**
 * 全局重登状态管理器
 * 防止 HTTP 和 WebSocket 重登冲突，避免重复重登
 */

class ReloginManager {
  constructor() {
    // 重登状态
    this.isReloginInProgress = false
    this.reloginAttempts = 0
    this.lastReloginTime = 0
    
    // 配置
    this.MAX_RELOGIN_ATTEMPTS = 3
    this.RELOGIN_COOLDOWN = 5000 // 5秒冷却时间
    this.RELOGIN_TIMEOUT = 30000 // 30秒超时
    
    // 重登回调队列
    this.reloginCallbacks = []
    this.reloginTimer = null
  }

  /**
   * 检查是否可以进行重登
   */
  canRelogin() {
    const now = Date.now()
    
    // 检查冷却期
    if (now - this.lastReloginTime < this.RELOGIN_COOLDOWN) {
      console.log('重登冷却期内，跳过重登')
      return false
    }
    
    // 检查最大重登次数
    if (this.reloginAttempts >= this.MAX_RELOGIN_ATTEMPTS) {
      console.log('已达到最大重登次数，禁止重登')
      return false
    }
    
    return true
  }

  /**
   * 开始重登
   * @param {Function} reloginFn 重登函数
   * @returns {Promise} 重登结果
   */
  startRelogin(reloginFn) {
    return new Promise((resolve, reject) => {
      // 检查是否可以重登
      if (!this.canRelogin()) {
        reject(new Error('Cannot relogin'))
        return
      }
      
      // 如果正在重登，加入队列等待
      if (this.isReloginInProgress) {
        console.log('重登进行中，加入等待队列')
        this.reloginCallbacks.push({ resolve, reject })
        return
      }
      
      // 开始重登
      this.isReloginInProgress = true
      this.reloginAttempts++
      this.lastReloginTime = Date.now()
      
      console.log(`开始第 ${this.reloginAttempts} 次重登`)
      
      // 设置超时
      this.reloginTimer = setTimeout(() => {
        console.log('重登超时')
        this.finishRelogin(false, new Error('Relogin timeout'))
      }, this.RELOGIN_TIMEOUT)
      
      // 执行重登
      try {
        const reloginPromise = reloginFn()
        
        if (reloginPromise && typeof reloginPromise.then === 'function') {
          reloginPromise
            .then(() => {
              this.finishRelogin(true)
              resolve()
            })
            .catch((error) => {
              this.finishRelogin(false, error)
              reject(error)
            })
        } else {
          // 同步函数，假设成功
          this.finishRelogin(true)
          resolve()
        }
      } catch (error) {
        this.finishRelogin(false, error)
        reject(error)
      }
    })
  }

  /**
   * 完成重登
   * @param {boolean} success 是否成功
   * @param {Error} error 错误信息
   */
  finishRelogin(success, error = null) {
    console.log(`重登完成，结果: ${success ? '成功' : '失败'}`)
    
    // 清除超时定时器
    if (this.reloginTimer) {
      clearTimeout(this.reloginTimer)
      this.reloginTimer = null
    }
    
    // 重置状态
    this.isReloginInProgress = false
    
    if (success) {
      // 重登成功，重置计数器
      this.reloginAttempts = 0
      this.lastReloginTime = 0
      
      // 通知所有等待的回调
      this.reloginCallbacks.forEach(callback => {
        callback.resolve()
      })
    } else {
      // 重登失败，通知所有等待的回调
      this.reloginCallbacks.forEach(callback => {
        callback.reject(error || new Error('Relogin failed'))
      })
    }
    
    // 清空回调队列
    this.reloginCallbacks = []
  }

  /**
   * 重置重登状态
   */
  reset() {
    console.log('重置重登状态')
    this.isReloginInProgress = false
    this.reloginAttempts = 0
    this.lastReloginTime = 0
    
    // 清除定时器
    if (this.reloginTimer) {
      clearTimeout(this.reloginTimer)
      this.reloginTimer = null
    }
    
    // 清空回调队列
    this.reloginCallbacks.forEach(callback => {
      callback.reject(new Error('Relogin reset'))
    })
    this.reloginCallbacks = []
  }

  /**
   * 获取重登状态信息
   */
  getStatus() {
    return {
      isReloginInProgress: this.isReloginInProgress,
      reloginAttempts: this.reloginAttempts,
      lastReloginTime: this.lastReloginTime,
      waitingCallbacks: this.reloginCallbacks.length
    }
  }
}

// 创建全局实例
const reloginManager = new ReloginManager()

export default reloginManager
