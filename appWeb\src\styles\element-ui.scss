//覆盖一些element-ui样式

// form
.el-form-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  &:last-of-type {
    margin-bottom: 24px;
  }
}
.el-form-item__error {
  color: var(--color-negative-400);
  margin-top: 10px;
  position: relative;
  user-select: none;
}
.el-form-item--medium .el-form-item__content {
  margin-left: 0 !important;
  flex: 1;
}
.el-form-item--medium .el-form-item__label {
  line-height: normal;
}
.el-checkbox__inner {
  width: 18px;
  height: 18px;
  background-color: transparent;
  border: 2px solid var(--color-primary);
  border-radius: 4px;
  box-sizing: border-box;
}
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--color-primary);
  border: none;
  box-sizing: border-box;
}
.el-checkbox__inner::after {
  top: 2px;
  width: 5px;
  height: 10px;
  border: 2.5px solid #fff;
  border-left: 0;
  border-top: 0;
  left: 6px;
  border-radius: 0 0 2px 0px;
}
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: var(--color-primary);
}

// dialog 居中
.el-dialog__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
  border-radius: 2px;
  margin-top: 0 !important;

  .el-dialog__body {
    padding: 24px 24px 0;
  }
  .el-dialog__footer {
    padding: 0 24px 24px;
  }
  .el-input__inner {
    height: 40px;
    line-height: 40px;
  }
}

.el-dialog__header {
  background: var(--color-neutral-500);
  height: 44px;
  line-height: 44px;
  padding: 0 0 0 28px;
  border-radius: 4px 4px 0 0;
  display: flex;
  align-items: center;
}
.el-dialog__headerbtn .el-dialog__close {
  color: var(--color-neutral-100);
  font-weight: bold;
  font-size: var(--font-size-h4);
}
.el-dialog__title {
  font-weight: bold;
  font-size: 16px;
  color: var(--color-neutral-100);
}
.el-dialog__headerbtn {
  top: 15px;
}
.el-dialog__body {
  font-weight: bold;
}

// 弹框里的按钮（确认/取消）
.el-dialog .el-button--medium {
  border-radius: 18px;
}
.el-dialog .el-button--primary {
  background: #2daada;
  border-color: #2daada;
}
.el-dialog .el-button--info {
  background: #ffffff;
  border-color: #ffffff;
  color: #3a4043;
}

// 下拉菜单
.el-dropdown {
  color: var(--color-neutral-200) !important;
}
.el-dropdown-menu {
  background: var(--color-neutral-500);
  border: none;
  max-height: 400px;
  overflow: auto;
  padding: 0;
  box-shadow: var(--box-shadow-pop);
}
.el-popper .popper__arrow {
  border-bottom-color: var(--color-neutral-500) !important;
  display: none;
}
.el-popper[x-placement^="top"] .popper__arrow {
  border-bottom-color: var(--color-neutral-500) !important;
  display: none;
}
.el-popper .popper__arrow::after {
  border-bottom-color: var(--color-neutral-500) !important;
  display: none;
}
.el-popper[x-placement^="top"] .popper__arrow::after {
  border-bottom-color: var(--color-neutral-500) !important;
  display: none;
}
.el-dropdown-menu__item {
  color: var(--color-neutral-100);
}
.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
  background: var(--color-neutral-hover);
  color: var(--color-neutral-100);
}
.el-dropdown-menu--medium .el-dropdown-menu__item {
  font-size: 14px;
  padding: 10px 12px;
  line-height: 20px;
}
.el-dropdown-menu__item.is-disabled {
  background: var(--color-neutral-hover);
  color: var(--color-primary);
}

// 滑块（调节音量）
.el-slider__runway {
  margin: 0;
  background: rgba(217, 230, 255, 0.5);
}
.el-slider__bar {
  background: var(--color-neutral-100);
}
.el-slider__button-wrapper {
  height: 14px;
  width: 6px;
  top: -4px;
}
.el-slider__button {
  width: 6px;
  height: 14px;
  border: none;
  border-radius: 4px;
  position: absolute;
  top: 0;
}

// 日历
.el-picker-panel {
  color: var(--color-neutral-500);
  border: 1px solid var(--color-neutral-500);
  background: var(--color-neutral-500);
}
.el-date-table td.disabled div {
  background: var(--color-neutral-500);
}
.el-date-table th,
.el-date-picker__header--bordered {
  color: var(--color-neutral-100);
  border-bottom: solid 1px var(--color-neutral-500);
}
.el-popper[x-placement^="top"] .popper__arrow,
.el-popper[x-placement^="top"] .popper__arrow::after {
  border-top-color: var(--color-neutral-500) !important;
  display: none;
}
.el-picker-panel__icon-btn,
.el-picker-panel__icon-btn:hover,
.el-date-picker__header-label,
.el-date-picker__header-label.active,
.el-date-picker__header-label:hover {
  color: #f4f9fd;
}
.el-date-picker__header {
  padding: 12px;
  background: #2daada;
}
.el-picker-panel__content {
  margin: 0 15px 15px;
}
.el-date-table td.disabled div {
  color: #727477;
}
.el-month-table td.disabled .cell,
.el-year-table td.disabled .cell {
  color: #727477;
  background: #272a2d;
}

// 输入框
.el-input__inner,
.el-textarea__inner {
  background-color: var(--color-neutral-500);
  border: none;
  color: var(--color-neutral-100);
}
.el-input input:focus {
  border: 1px solid var(--color-primary) !important;
}
.el-textarea textarea:focus {
  border: 1px solid var(--color-primary);
}
.el-input input::-webkit-input-placeholder {
  color: var(--color-neutral-300);
}
.el-textarea textarea::-webkit-input-placeholder {
  color: var(--color-neutral-300);
}
.el-input input::-moz-input-placeholder {
  color: var(--color-neutral-300);
}
.el-textarea textarea::-moz-input-placeholder {
  color: var(--color-neutral-300);
}
.el-input input::-ms-input-placeholder {
  color: var(--color-neutral-300);
}
.el-textarea textarea::-ms-input-placeholder {
  color: var(--color-neutral-300);
}

//表格
// 表格为空时 暂无数据的图片
.el-empty {
  padding: 160px 0;
}
// 表格为空时 暂无数据 图片下的文字
.el-empty__description {
  margin-top: 12px;
}
.el-table__empty-text {
  line-height: 22px;
}
.el-empty__description p {
  font-size: 16px;
  color: #898d95;
}

// 列表
.el-table {
  background-color: var(--color-neutral-600);
  border-radius: 6px;
  &::before {
    height: 0;
  }
  tr {
    background-color: unset;
  }
  th.el-table__cell {
    background-color: var(--color-table-header);
  }
  td.el-table__cell,
  th.el-table__cell.is-leaf {
    border-bottom: 1px solid var(--color-neutral-700);
  }
  td.infoText div {
    color: var(--color-neutral-200);
  }
  th.el-table__cell > .cell {
    padding-left: 14px;
  }
  div,
  text {
    color: var(--color-neutral-100);
    font-size: var(--font-size-normal);
    font-family: Arial, Helvetica, sans-serif;
  }
  .cell {
    padding: 0;
  }
  th.el-table__cell > .cell {
    padding: 0;
  }
}
.el-table--striped {
  .el-table__body tr.el-table__row--striped td.el-table__cell {
    background: unset !important;
  }
  .el-table__body tr.el-table__row--striped:hover {
    background-color: var(--color-neutral-hover) !important;
  }
  .el-table__body tr.current-row > td.el-table__cell {
    background-color: var(--color-neutral-hover);
  }
}

.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: var(--color-neutral-hover) !important;
}
.el-table--medium .el-table__cell {
  padding: 14px;
}

// 分页器
.pagination-container {
  padding: 20px !important;
  background-color: unset !important;
}
.el-pagination .btn-next,
.el-pagination .btn-prev {
  background: center center no-repeat var(--color-neutral-500);
  color: var(--color-neutral-200);
}
.el-dialog,
.el-pager li {
  background-color: var(--color-neutral-500);
  color: var(--color-neutral-200);
}
.el-pager li.btn-quicknext,
.el-pager li.btn-quickprev {
  color: var(--color-neutral-200);
}
.el-pager li.active {
  color: var(--color-primary);
}
.el-pagination button:disabled {
  background-color: var(--color-neutral-500);
  opacity: 0.8;
}
.el-pagination__jump {
  color: var(--color-neutral-200);
  margin-left: 8px;
  height: 100% !important;
}
.el-select .el-input .el-select__caret {
  font-size: var(--font-size-large);
  font-weight: bolder;
  color: var(--color-neutral-100);
}
.el-pagination {
  --height-pagination: 38px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  .el-pagination__total {
    color: var(--color-neutral-200);
    margin-right: 2px;
    font-size: var(--font-size-normal) !important;
    line-height: var(--height-pagination);
  }
  .el-select .el-input .el-input__inner {
    padding: 7px 36px 7px 12px;
    font-size: var(--font-size-normal);
    box-sizing: border-box;
    height: var(--height-pagination);
  }
  .el-input__suffix {
    right: 10px;
  }
  .el-pager {
    display: inline-flex;
    gap: 8px;
    .number,
    .btn-quicknext,
    .btn-quickprev {
      padding: 0 12px;
      box-sizing: border-box;
      border-radius: 6px;
      height: var(--height-pagination);
      line-height: var(--height-pagination);
      font-weight: normal;
    }
  }
  .btn-next,
  .btn-prev {
    border-radius: 6px;
    height: var(--height-pagination);
    line-height: var(--height-pagination);
    padding: 0 12px;
    font-weight: normal;
  }
  .btn-next {
    margin-left: 8px;
  }
  .btn-prev {
    margin-right: 8px;
  }
  .el-select .el-input {
    margin-right: 0;
  }
  .el-pagination__sizes {
    margin-right: 8px;
    height: var(--height-pagination) !important;
    line-height: var(--height-pagination) !important;
  }
  .el-pagination__editor.el-input {
    margin: 0 10px;
    height: var(--height-pagination);
    line-height: 38px;
    width: auto;
  }
  .el-pagination__editor.el-input .el-input__inner {
    box-sizing: border-box;
    height: var(--height-pagination);
    line-height: var(--height-pagination);
    padding: 0 24px;
    width: auto;
  }
  .el-pagination__editor.el-input .el-input__inner {
    width: 66px;
    padding: 9px 0;
  }
}

// 日期选择器
.el-range-input {
  background-color: var(--color-neutral-500);
}
.el-date-editor .el-range-separator {
  color: var(--color-neutral-200);
}
.el-date-editor .el-range-input {
  color: var(--color-neutral-100);
  width: 130px;
}
.el-range-editor--medium .el-range-input::-webkit-input-placeholder {
  color: var(--color-neutral-300);
}
.el-date-editor .el-range__icon {
  color: var(--color-neutral-200);
  width: 18px;
  height: 18px;
  line-height: var(--font-size-large);
  margin-right: 8px;
  margin-left: 0px;
}
.el-icon-date:before {
  font-size: var(--font-size-large);
}
.el-date-editor .el-range__close-icon {
  color: var(--color-neutral-100);
  font-size: var(--font-size-normal);
  padding-top: 2px;
}
.el-date-range-picker__content.is-left {
  border-right: 1px solid var(--color-neutral-700);
}
.el-date-range-picker .el-picker-panel__content {
  background-color: var(--color-neutral-500);
}
.el-date-range-picker__content.is-left {
  border-radius: 6px 0 0 6px;
}
.el-date-range-picker__content.is-right {
  border-radius: 0 6px 6px 0;
}
.el-date-table th,
.el-date-picker__header--bordered {
  border-bottom: none;
}
.el-date-picker__header,
.el-picker-panel__footer {
  background-color: var(--color-neutral-500);
}
.el-picker-panel__footer {
  border-top: 1px solid #555;
}
.el-date-picker__time-header {
  border-bottom: 1px solid #555;
}
.el-picker-panel.el-date-picker.el-popper.has-time
  > div.el-picker-panel__footer
  > button.el-button.el-picker-panel__link-btn.el-button--default.el-button--mini.is-plain {
  border: none;
  color: var(--color-neutral-100);
  &:hover {
    background: none !important;
    color: var(--color-neutral-200);
  }
}
div.el-picker-panel.el-date-picker.el-popper.has-time
  > div.el-picker-panel__body-wrapper
  > div
  > div.el-date-picker__time-header
  > span:nth-child(2)
  > div.el-time-panel.el-popper
  > div.el-time-panel__content
  > div
  > div.el-scrollbar {
  box-shadow: none;
  --webkit-box-shadow: none;
}
.el-date-table td span {
  font-size: var(--font-size-normal);
  color: var(--color-neutral-100);
}
.el-icon-arrow-right:before,
.el-icon-arrow-left:before,
.el-icon-d-arrow-left:before,
.el-icon-d-arrow-right:before {
  font-weight: 700;
  font-size: var(--font-size-normal);
}
.el-date-table th {
  font-size: var(--font-size-normal);
}
.el-picker-panel__body {
  border-radius: 6px;
  box-shadow: var(--box-shadow-pop);
}
.el-date-range-picker__content .el-date-range-picker__header div {
  color: var(--color-neutral-100);
  font-size: var(--font-size-normal);
}
.el-date-table__row .prev-month span,
.el-date-table__row .next-month span {
  color: var(--color-neutral-400);
}
.el-date-table td.end-date span,
.el-date-table td.start-date span {
  background-color: var(--color-primary);
}
.el-date-table td.today span {
  color: var(--color-primary);
}
.el-date-table td.in-range div {
  background-color: var(--color-date-range);
}
.el-date-table td div {
  padding: 1px;
  height: 28px;
  // width: 28px;
  line-height: 28px;
  box-sizing: border-box;
  &:hover {
    background-color: var(--color-neutral-hover);
  }
}
.el-date-table td.in-range div,
.el-date-table td.in-range div:hover {
  background-color: var(--color-date-range);
}
.el-range-editor--medium .el-range-separator {
  line-height: 33px;
}

// loading-mask
.el-loading-mask {
  background-color: var(--color-neutral-500);
}

// 普通下拉框
.el-select-dropdown {
  border-radius: 6px;
  background-color: var(--color-neutral-500);
}
.el-scrollbar {
  border-radius: 6px;
  overflow: hidden;
}
.el-select-dropdown__list {
  padding: 0;
  border: none;
}
.el-select-dropdown__item {
  background-color: var(--color-neutral-500);
  color: var(--color-neutral-100);
  padding: 10px 12px;
  height: auto;
  line-height: auto;
}
.el-select-dropdown__item.hover,
.el-select-dropdown__item.selected {
  color: var(--color-primary);
  background-color: var(--color-neutral-hover);
  font-weight: normal;
}
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover,
.el-select-dropdown__item.is-disabled:hover {
  background-color: var(--color-neutral-hover);
}
.el-popper {
  border: none;
}

// 表单
.el-form-item__label {
  color: var(--color-neutral-100);
  font-weight: normal;
  position: relative;
  padding-left: 8px;
  word-break: normal;
  &::before {
    position: absolute;
    left: 4px;
    transform: translateX(-100%);
    color: var(--color-negative-400) !important;
  }
}
.el-checkbox__label {
  color: var(--color-neutral-100);
}
.el-checkbox__input.is-checked + .el-checkbox__label {
  color: var(--color-primary);
}
.el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  height: 3px;
}
.el-button {
  background: none;
  line-height: normal;
}
.el-form-item--medium .el-form-item__content {
  color: var(--color-neutral-100);
}

.el-message {
  width: calc(100vw - 20px);
  border: none;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  top: 10px !important;
  box-shadow: var(--box-shadow-pop);
}
.el-message__content {
  font-weight: bolder;
}
.el-message--success {
  background-color: #293d31;
  color: #b6db3a;
}
.el-message--info {
  background-color: #3c455f;
}
.el-message--info .el-message__content {
  color: var(--color-neutral-100);
}
.el-message .el-icon-info {
  color: var(--color-neutral-100);
}
.el-message--error {
  background-color: #452d2d;
  color: var(--color-negative-400);
}

.el-scrollbar {
  box-shadow: var(--box-shadow-pop);
}

.el-switch__core {
  height: 24px !important;
  width: 50px !important;
  border-radius: 26px;
}
.el-switch__core:after {
  width: 22px;
  height: 22px;
  top: 0;
  background-color: var(--color-neutral-100);
}
.el-switch.is-checked .el-switch__core::after {
  margin-left: -22px;
}

.el-time-panel.el-popper {
  background-color: var(--color-neutral-500);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.el-time-spinner__item {
  color: var(--color-neutral-200);
  &.active {
    color: var(--color-neutral-100) !important;
  }
  &:hover {
    background-color: var(--color-neutral-400) !important;
  }
}
.el-time-panel__footer {
  border-top: 1px solid var(--color-neutral-400);
}
.el-time-panel__btn.cancel {
  color: var(--color-neutral-200);
  &:hover {
    color: var(--color-neutral-100);
  }
}
.el-time-panel__btn.confirm {
  color: var(--color-primary-202);
  &:hover {
    color: var(--color-primary);
  }
}
.el-time-panel__content::after,
.el-time-panel__content::before {
  border-top: 1px solid var(--color-neutral-300);
  border-bottom: 1px solid var(--color-neutral-300);
}
.el-button + .el-button {
  margin-left: 0;
}
.operateButtonLine {
  display: flex;
  column-gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.el-radio-group label {
  color: var(--color-neutral-200);
}
