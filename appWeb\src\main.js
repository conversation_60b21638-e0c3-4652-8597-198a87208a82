import Vue from 'vue'
import Cookies from 'js-cookie'
import Element from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import '@/styles/index.scss' // global css

import App from './App'
import router from './router'
import store from './store'

import i18n from './lang' // Internationalization
import './icons' // icon
import './permission' // permission control

import md5 from 'js-md5'

Vue.prototype.$md5 = md5

Vue.use(Element, {
  size: Cookies.get('size') || 'medium', // set element-ui default size
  i18n: (key, value) => i18n.t(key, value)
})

Vue.prototype.base_url = process.env.BASE_API
Vue.config.productionTip = false

document.title =  i18n.t('mgtPlat')

// 全局注册websocket
import websocket from './utils/websocket.js'
Vue.prototype.$websocket = websocket

// 初始化 Tab 管理器
import tabManager from './utils/tabManager.js'
Vue.prototype.$tabManager = tabManager

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: h => h(App)
})
