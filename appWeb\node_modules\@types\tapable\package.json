{"_args": [["@types/tapable@1.0.2", "D:\\---AProjects---\\mirruServer\\miruserver\\appWeb"]], "_development": true, "_from": "@types/tapable@1.0.2", "_id": "@types/tapable@1.0.2", "_inBundle": false, "_integrity": "sha1-4TGC4baYcaQi14Y+EaSm9bgUpL0=", "_location": "/@types/tapable", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/tapable@1.0.2", "name": "@types/tapable", "escapedName": "@types%2ftapable", "scope": "@types", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/html-webpack-plugin"], "_resolved": "https://registry.npmjs.org/@types/tapable/-/tapable-1.0.2.tgz", "_spec": "1.0.2", "_where": "D:\\---AProjects---\\mirruServer\\miruserver\\appWeb", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "e-cloud", "url": "https://github.com/e-cloud"}], "dependencies": {}, "description": "TypeScript definitions for tapable", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/tapable", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typeScriptVersion": "2.3", "typesPublisherContentHash": "87260b7dd8eb3a6cac1f9d3d032580e48789a47cc92c6d5129f629fa9439ae4a", "version": "1.0.2"}