{"_args": [["@types/q@1.5.1", "D:\\---AProjects---\\mirruServer\\miruserver\\appWeb"]], "_development": true, "_from": "@types/q@1.5.1", "_id": "@types/q@1.5.1", "_inBundle": false, "_integrity": "sha1-SP2YwVYf5xi2FzPa7Ub/EVtJbhg=", "_location": "/@types/q", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/q@1.5.1", "name": "@types/q", "escapedName": "@types%2fq", "scope": "@types", "rawSpec": "1.5.1", "saveSpec": null, "fetchSpec": "1.5.1"}, "_requiredBy": ["/coa"], "_resolved": "https://registry.npmjs.org/@types/q/-/q-1.5.1.tgz", "_spec": "1.5.1", "_where": "D:\\---AProjects---\\mirruServer\\miruserver\\appWeb", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/bnemetchek"}, {"name": "<PERSON>", "url": "https://github.com/AndrewGaspar"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON>", "url": "https://github.com/mboudreau"}, {"name": "TeamworkGuy2", "url": "https://github.com/TeamworkGuy2"}], "dependencies": {}, "description": "TypeScript definitions for Q", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/q", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typeScriptVersion": "2.3", "typesPublisherContentHash": "da62bdcf3d8485f5aec9e1153d462ed4cd770ba38a39ba06f35da755de1cd2a2", "version": "1.5.1"}