<template>
  <el-dropdown
    trigger="click"
    class="international"
    @command="handleSetLanguage"
    @visible-change="langListVisibleChange"
  >
    <!-- <div>
      <svg-icon class-name="international-icon" icon-class="language" />
    </div> -->
    <span class="pointer">
      {{ language_text
      }}<img
        v-if="lang_dropdown_visible"
        class="topBottomArrowIcon"
        src="../../assets/img/topArrow.png"
      /><img
        v-else
        class="topBottomArrowIcon"
        src="../../assets/img/bottomArrow.png"
      />
    </span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item :disabled="language === 'ky'" command="ky"
        >Кыргызча</el-dropdown-item
      >
      <el-dropdown-item :disabled="language === 'en'" command="en"
        >English</el-dropdown-item
      >
      <el-dropdown-item :disabled="language === 'ru'" command="ru"
        >Русский</el-dropdown-item
      >
      <el-dropdown-item
        v-if="!isProd"
        :disabled="language === 'zh'"
        command="zh"
        >中文</el-dropdown-item
      >
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  data () {
    return {
      lang_dropdown_visible: false,
    }
  },
  computed: {
    language () {
      return this.$store.getters.language
    },
    language_text () {
      const lang = this.$store.getters.language
      let language_text = 'Кыргызча'
      if (lang === 'zh') {
        language_text = '中文'
      } else if (lang === 'en') {
        language_text = 'English'
      } else if (lang === 'ru') {
        language_text = 'Русский'
      } else if (lang === 'ky') {
        language_text = 'Кыргызча'
      }
      return language_text
    },
    isProd () {
      return process.env.NODE_ENV === 'production'
    }
  }
  ,
  methods: {
    handleSetLanguage (lang) {
      this.$i18n.locale = lang
      this.$store.dispatch('setLanguage', lang).then(() => {
        this.$emit('handleSetLanguage', lang)
      })
      // setTimeout(() => {
      //   location.reload()
      // }, 100);
    },
    // 语言列表的下拉框出现/隐藏时触发 用来切换图标
    langListVisibleChange (val) {
      this.lang_dropdown_visible = val
    },
  }
};
</script>

<style scoped>
.international-icon {
  font-size: 20px;
  cursor: pointer;
  vertical-align: -5px !important;
}
.topBottomArrowIcon {
  width: 18px;
  height: 18px;
  vertical-align: middle;
}
</style>

