{"_args": [["@types/minimatch@3.0.5", "D:\\---AProjects---\\mirruServer\\miruserver\\appWeb"]], "_development": true, "_from": "@types/minimatch@3.0.5", "_id": "@types/minimatch@3.0.5", "_inBundle": false, "_integrity": "sha512-Klz949h02Gz2uZCMGwDUSDS1YBlTdDDgbWHi+81l29tQALUtvz4rAYi5uoVhE5Lagoq6DeqAUlbrHvW/mXDgdQ==", "_location": "/@types/minimatch", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/minimatch@3.0.5", "name": "@types/minimatch", "escapedName": "@types%2fminimatch", "scope": "@types", "rawSpec": "3.0.5", "saveSpec": null, "fetchSpec": "3.0.5"}, "_requiredBy": ["/@types/glob"], "_resolved": "https://registry.npmmirror.com/@types/minimatch/-/minimatch-3.0.5.tgz", "_spec": "3.0.5", "_where": "D:\\---AProjects---\\mirruServer\\miruserver\\appWeb", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "vvakame", "url": "https://github.com/vvakame"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/shantmarouti"}], "dependencies": {}, "description": "TypeScript definitions for Minimatch", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/minimatch", "license": "MIT", "main": "", "name": "@types/minimatch", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/minimatch"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "ce8670ab7ddb0b32136aa0f819c3e7d791e75f04ff991f2f1baa3a9967dd61c0", "version": "3.0.5"}