<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Fix Test</title>
</head>
<body>
    <h1>WebSocket Fix Test</h1>
    <div id="test-results"></div>

    <script>
        // 模拟测试WebSocket修复
        function testWebSocketFix() {
            const results = [];

            // 测试1: socket初始化为null
            let socket = null;
            try {
                if (socket && socket instanceof WebSocket && socket.readyState !== WebSocket.CLOSED) {
                    socket.close();
                }
                results.push("✅ Test 1 passed: null socket check works");
            } catch (error) {
                results.push("❌ Test 1 failed: " + error.message);
            }

            // 测试2: socket为空对象
            socket = {};
            try {
                if (socket && socket instanceof WebSocket && socket.readyState !== WebSocket.CLOSED) {
                    socket.close();
                }
                results.push("✅ Test 2 passed: empty object socket check works");
            } catch (error) {
                results.push("❌ Test 2 failed: " + error.message);
            }

            // 测试3: socket为WebSocket实例但已关闭
            try {
                socket = new WebSocket('ws://localhost:8080'); // 这会失败但不影响测试
            } catch (e) {
                // 创建一个模拟的WebSocket对象
                socket = {
                    readyState: 3, // WebSocket.CLOSED
                    close: function() { console.log('close called'); }
                };
                Object.setPrototypeOf(socket, WebSocket.prototype);
            }

            try {
                if (socket && socket instanceof WebSocket && socket.readyState !== WebSocket.CLOSED) {
                    socket.close();
                }
                results.push("✅ Test 3 passed: closed WebSocket check works");
            } catch (error) {
                results.push("❌ Test 3 failed: " + error.message);
            }

            // 测试4: webSocketSend检查
            try {
                socket = null;
                if (!socket || !(socket instanceof WebSocket) || (socket && socket.readyState !== 1)) {
                    results.push("✅ Test 4 passed: webSocketSend check works for null socket");
                } else {
                    results.push("❌ Test 4 failed: webSocketSend check failed");
                }
            } catch (error) {
                results.push("❌ Test 4 failed: " + error.message);
            }

            // 测试5: webSocketSend检查空对象
            try {
                socket = {};
                if (!socket || !(socket instanceof WebSocket) || (socket && socket.readyState !== 1)) {
                    results.push("✅ Test 5 passed: webSocketSend check works for empty object");
                } else {
                    results.push("❌ Test 5 failed: webSocketSend check failed");
                }
            } catch (error) {
                results.push("❌ Test 5 failed: " + error.message);
            }

            // 测试6: MiruWebsocket socket属性访问
            try {
                // 模拟MiruWebsocket类
                function MockMiruWebsocket() {
                    let mockSocket = null;
                    Object.defineProperty(this, 'socket', {
                        get: function() {
                            return mockSocket;
                        }
                    });
                    this.setSocket = function(s) { mockSocket = s; };
                }

                const mockWs = new MockMiruWebsocket();

                // 测试null socket
                if (mockWs.socket === null) {
                    results.push("✅ Test 6a passed: socket property returns null when not initialized");
                } else {
                    results.push("❌ Test 6a failed: socket property should return null");
                }

                // 测试设置socket后的访问
                const mockSocketObj = { readyState: 1, close: function() {} };
                mockWs.setSocket(mockSocketObj);
                if (mockWs.socket === mockSocketObj) {
                    results.push("✅ Test 6b passed: socket property returns correct socket object");
                } else {
                    results.push("❌ Test 6b failed: socket property should return set socket");
                }

            } catch (error) {
                results.push("❌ Test 6 failed: " + error.message);
            }

            return results;
        }

        // 运行测试
        const testResults = testWebSocketFix();
        const resultsDiv = document.getElementById('test-results');
        resultsDiv.innerHTML = '<h2>Test Results:</h2><ul>' +
            testResults.map(result => '<li>' + result + '</li>').join('') +
            '</ul>';

        console.log('WebSocket Fix Test Results:', testResults);
    </script>
</body>
</html>
