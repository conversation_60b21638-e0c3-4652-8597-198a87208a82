import router from './router'
import store from './store'
// import { Message } from 'element-ui'
import { getToken, removeToken, removeRole } from '@/utils/auth'
import tabManager from '@/utils/tabManager'

const whiteList = ['/login'] // no redirect whitelist

router.beforeEach((to, from, next) => {
  if (getToken() && store.getters.token) {
    // has token
    if (to.path === '/login' || to.path === '/') {
      store.commit('SET_TOKEN', '')
      store.commit('SET_ROLES', [])
      store.commit('SET_ROLE', '')
      next({ path: '/' })
      removeToken()
      removeRole()
    } else {
      if (store.getters.addRouters.length === 0) {
        const username = sessionStorage.username || tabManager.getAccount()
        if (username) {
          // 如果刷新导致 sessionStorage 丢失，则用持久化的账号补全
          if (!sessionStorage.username) {
            sessionStorage.username = username
          }
          const token = store.getters.role
          store.dispatch('GenerateRoutes', { token }).then(() => { // 有token就生成可访问的路由表
            router.addRoutes(store.getters.addRouters) // 动态添加可访问路由表
            next({ ...to, replace: true }) // 确保addRoutes已完成
          })
        } else {
          store.commit('SET_TOKEN', '')
          store.commit('SET_ROLES', [])
          store.commit('SET_ROLE', '')
          next({ path: '/' })
          removeToken()
          removeRole()
        }
      } else {
        next() // 当有用户权限的时候，说明所有可访问路由已生成
      }
    }
  } else {
    console.log('没有token')
    // has no token
    if (whiteList.indexOf(to.path) !== -1) { // 在免登录白名单，直接进入
      next()
    } else {
      next('/login') // 否则全部重定向到登录页
    }
  }
})

router.afterEach(() => {
})
