<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    @close="hideDialog"
  >
    <section>
      <el-form
        ref="dialogForm"
        :model="dialogForm.data"
        label-width="240px"
        label-position="left"
      >
        <el-form-item label="FPS" prop="group_name">
          <el-select
            v-model="selectedFps"
            :placeholder="$t('pleaseSelect')"
            style="width: 200px"
            @change="onChangeSelectFps"
          >
            <el-option
              v-for="item in fpsRange"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="!sn" :label="$t('range')" prop="range">
          <el-radio-group v-model="selectedRange">
            <el-radio v-if="group_id && group_id != -1" :label="0">{{
              $t("currentGroup")
            }}</el-radio>
            <el-radio :label="1">{{ $t("allGroup") }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </section>
    <span slot="footer" class="dialog-footer">
      <el-button class="tipCancelBtn" type="info" @click="hideDialog()">{{
        $t("cancel")
      }}</el-button>
      <el-button
        class="tipSubmitBtn"
        :loading="loading"
        type="primary"
        @click="submit()"
        >{{ $t("sure") }}</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { validSN } from '@/utils/validate'
import { editDeviceName, setFps, batchSetFps } from '@/api/deviceMgt'
export default {
  name: 'DeviceAddDialog',
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data () {

    return {
      dialogVisible: false,
      loading: false,
      dialogForm: {

      },

      group_id: '',
      sn: '',

      selectedFps: 25,
      fpsRange: [
        {
          label: '1',
          value: 1
        },
        {
          label: '5',
          value: 5
        },
        {
          label: '12',
          value: 12
        },
        {
          label: '25',
          value: 25
        },
      ],

      selectedRange: 0
    }
  },
  methods: {
    show (group_id, sn, val) {
      if (val) this.selectedFps = val
      this.group_id = group_id
      this.sn = sn
      if (group_id == -1)
        this.selectedRange = 1
      else
        this.selectedRange = 0
      this.dialogVisible = true
    },
    hideDialog () {
      this.dialogVisible = false
    },
    submit () {
      console.log(this.group_id, this.sn, this.selectedFps, this.selectedRange)

      if (this.sn) {
        // 单独设置fps
        this.setDeviceFps(this.sn, this.selectedFps)
      } else {
        // 批量设置fps
        this.batchSetDeviceFps()
      }
    },
    setDeviceFps (sn, value) {
      const reqData = {
        cid: sn,
        fps: value
      }
      this.loading = true
      setFps(reqData)
        .then(res => {
          if (res.code === 200) {
            this.$emit('refresh')
            this.loading = false
            this.$message.success(this.$t('modifySuccess'))
            this.hideDialog()
          }
        })
        .catch(err => {
          this.loading = false;
        })
    },
    batchSetDeviceFps () {
      const reqData = {
        group_id: this.group_id == '-1' || this.selectedRange == 1 ? '' : this.group_id,
        fps: this.selectedFps
      }
      this.loading = true
      batchSetFps(reqData)
        .then(res => {
          if (res.code === 200) {
            this.$emit('refresh')
            this.loading = false
            this.$message.success(this.$t('modifySuccess'))
            this.hideDialog()
          }
        }).catch(err => {
          this.loading = false
        })
    },
    onChangeSelectFps (val) {
      console.log(val)
    }
  }
}
</script>

<style scoped>
/deep/ .el-dialog {
  width: 400px;
  border-radius: 4px;
}

/deep/ .el-radio-group label:first-of-type {
  margin-bottom: 12px;
}
</style>

