@import "./element-ui.scss";

:root {
  --color-primary-dark: #0041c7;
  --color-primary: #1a75ff;
  --color-primary-202: #2295ff;
  --color-neutral-100: #d9e6ff;
  --color-neutral-200: #aebad9;
  --color-neutral-300: #8f99b2;
  --color-neutral-400: #70788c;
  --color-neutral-500: #3c404b;
  --color-neutral-600: #292c33;
  --color-neutral-700: #13141a;
  --color-neutral-hover: #32363e;
  --color-black: #000000;
  --color-white: #ffffff;
  --color-accent-400: #00a541;
  --color-warning-400: #b6db3a;
  --color-negative-300: #fd7486;
  --color-negative-400: #ff5268;
  --color-table-header: #12438d;
  --color-date-range: #47526c;
  --font-size-h1: 26px;
  --font-size-h2: 24px;
  --font-size-h3: 22px;
  --font-size-h4: 20px;
  --font-size-large: 18px;
  --font-size-medium: 17px;
  --font-size-regular: 16px;
  --font-size-normal: 14px;
  --font-size-small: 12px;

  --box-shadow-pop: 0px 20px 40px 0px rgba(0, 0, 0, 0.6);
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    Source Han Sans CN-Bold,
    Source Han Sans CN;
  margin: 0;
  padding: 0;
  background-color: var(--color-neutral-700);
  color: var(--color-neutral-100);
  font-size: var(--font-size-normal);
}

html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

#app,
.app-wrapper {
  min-width: 1270px;
  min-height: 100vh;
}

//main-container全局样式
.main-container {
  //min-height: calc(100vh - 75px);
}
.app-main {
  width: 100%;
  height: 100%;
  position: relative;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.pointer {
  cursor: pointer;
}

.el-input--medium .el-input__inner,
.el-range-editor--medium.el-input__inner {
  height: 40px;
}

.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-nowrap {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
// 文本超过2行的省略
.lineClamp2 {
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
// 文本超过3行的省略
.lineClamp3 {
  display: -webkit-box;
  line-clamp: 3;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.width100 {
  width: 100%;
}

.marginAuto {
  margin: 0 auto;
}
.marLeft10 {
  margin-left: 10px;
}
.marRgt10 {
  margin-right: 10px;
}
.marRgt20 {
  margin-right: 20px;
}
.marBtm10 {
  margin-bottom: 10px;
}
.marBtm20 {
  margin-bottom: 20px;
}

.el-dialog {
  background-color: var(--color-neutral-600);
  .dialog-footer {
    display: flex;
    justify-content: space-around;
    gap: 12px;
  }
  .tipCancelBtn,
  .tipSubmitBtn, {
    padding: 10px 0;
    height: auto;
    width: 140px;
  }
  .tipCancelBtn {
    flex: 1;
  }
  .tipSubmitBtn {
    flex: 1;
    margin-left: 0;
  }
}
.tipCancelBtn,
.tipSubmitBtn,
.tipDeleteBtn {
  padding: 10px 20px;
  height: auto;
  border-radius: 36px;
    border: none;
  height: 40px;
}
.tipDeleteBtn {
  background-color: var(--color-negative-400);
  color: var(--color-white);
  &:hover,
  &:focus {
    background-color: var(--color-negative-300);
    color: var(--color-white);
  }
}
.tipCancelBtn,
.el-dialog .tipCancelBtn {
  background-color: var(--color-neutral-500);
  color: var(--color-neutral-100);

  &:hover,
  &:focus {
    background-color: var(--color-neutral-400);
    color: var(--color-neutral-100);
  }
}
.tipSubmitBtn,
.el-dialog .tipSubmitBtn {
  background-color: var(--color-primary);
  color: var(--color-white);
  &:hover,
  &:focus {
    background-color: var(--color-primary-202);
    color: var(--color-white);
  }
  &[disabled] {
    background-color: var(--color-primary-dark);
    color: var(--color-white);
  }
}

.el-input--medium .el-input__icon {
  // display: inline-block;
  // background-image: url('../assets/img/eye.png');
  // background-size: contain;
  // background-repeat: no-repeat;
  // content: ' ';
}

.formItemTop {
  align-items: flex-start;
}

.el-dialog {
  .el-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  .el-checkbox {
    display: block;
    height: auto;
    display: flex;
    align-items: center;
  }
}

.primaryBtn {
  background: var(--color-primary);
  font-size: var(--font-size-large);
  color: var(--color-white);
  border-radius: 46px;
  line-height: 25px;
  padding: 12px 16px;
  :hover {
    background: var(--color-primary-202);
  }
}
.primaryDisabledBtn {
  background: var(--color-primary-dark) !important;
  color: var(--color-primary) !important;
}

.dot {
  width: 6px;
  height: 6px;
  display: inline-block;
  border-radius: 3px;
}
.onDot {
  background: var(--color-warning-400);
}
.offDot {
  background: var(--color-negative-400);
}

/* 筛选框 */
.filterBox {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}
.filterItem {
  display: flex;
  gap: 10px;
  align-items: center;
  color: var(--neutral-100);
}
.filterItem span {
  white-space: nowrap;
}
/* 筛选框 end */
