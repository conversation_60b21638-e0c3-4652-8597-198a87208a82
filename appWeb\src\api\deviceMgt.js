import request from "@/utils/request";

// 编辑设备组
export function editDeviceGroup (data) {
  return request({
    url: "v1/miru/camera/group/edit",
    method: "post",
    data
  });
}

// 获取设备组列表
export function getDeviceGroupList (data) {
  return request({
    url: "v1/miru/camera/group/list",
    method: "post",
    data
  });
}

// 获取设备组详情
export function getDeviceGroupDetail (data) {
  return request({
    url: "v1/miru/camera/group/info",
    method: "post",
    data
  });
}

// 获取设备列表
export function getDeviceList (data) {
  return request({
    url: "v1/miru/camera/list",
    method: "post",
    data
  });
}

// 编辑设备昵称
export function editDeviceName (data) {
  return request({
    url: "v1/miru/camera/edit",
    method: "post",
    data
  });
}

// 编辑设备昵称
export function deleteDevice (data) {
  return request({
    url: "v1/miru/camera/delete",
    method: "post",
    data
  });
}

// 删除设备组
export function deleteDeviceGroup (data) {
  return request({
    url: "v1/miru/camera/group/delete",
    method: "post",
    data
  });
}

// 设置fps
export function setFps (data) {
  return request({
    url: "v1/miru/camera/fps/set",
    method: "post",
    data
  });
}

// 批量设置fps
export function batchSetFps (data) {
  return request({
    url: "v1/miru/camera/fps/batch/set",
    method: "post",
    data
  });
}
