<template>
  <div class="navbar">
    <div class="infoBox">
      <div class="platInfo">
        <img class="platLogo" src="../../../assets/img/home_logo.png" />
        <div class="platNameBox">
          <span class="platName">{{ $t("mgtPlat") }}</span>
          <span class="platNameTip">{{ $t("mgtPlatTip") }}</span>
        </div>
      </div>
      <div class="menuCard">
        <div
          :class="{ selectedSubMenu: selected_menu_flag === 'home' }"
          class="subMenu"
          @click="toggleMenu('home')"
        >
          {{ $t("home") }}
        </div>
        <div
          v-if="isSuperAdmin || isNornalAdmin"
          :class="{ selectedSubMenu: selected_menu_flag === 'device' }"
          class="subMenu"
          @click="toggleMenu('device')"
        >
          {{ $t("deviceManagement") }}
        </div>
        <div
          v-if="isSuperAdmin"
          :class="{ selectedSubMenu: selected_menu_flag === 'user' }"
          class="subMenu"
          @click="toggleMenu('user')"
        >
          {{ $t("userManagement") }}
        </div>
        <div
          v-if="isSuperAdmin"
          :class="{
            selectedSubMenu: selected_menu_flag === 'video_management',
          }"
          class="subMenu"
          @click="toggleMenu('video_management')"
        >
          {{ $t("videomanagement") }}
        </div>
        <div
          v-if="isSuperAdmin || isNornalAdmin"
          :class="{ selectedSubMenu: selected_menu_flag === 'system' }"
          class="subMenu"
          @click="toggleMenu('system')"
        >
          {{ $t("systemSetting") }}
        </div>
        <div
          v-if="isSuperAdmin"
          :class="{ selectedSubMenu: selected_menu_flag === 'logs' }"
          class="subMenu"
          @click="toggleMenu('logs')"
        >
          {{ $t("operationLogs") }}
        </div>
      </div>

      <div class="navbar__right">
        <lang-select
          class="set-language"
          @handleSetLanguage="handleSetLanguage"
        />
        <span class="userName">{{ username }}</span>
        <div class="logOut pointer" @click="logoutDialog">
          {{ $t("quitTitle") }}
        </div>
      </div>

      <!-- !!test!! -->
      <!-- <button
        @click="test"
        style="
          margin-left: 20px;
          padding: 5px 10px;
          background: #409eff;
          color: white;
          border: none;
          border-radius: 3px;
          cursor: pointer;
        "
      >
        Test 401
      </button> -->
    </div>
    <tip-dialog
      ref="tipDialog"
      :title="dialogTitle"
      :tip="dialogTip"
      :request="tipRequest"
      :params="tipParams"
      @handleTip="backTip"
    />
  </div>
</template>

<script>
import LangSelect from '@/components/LangSelect'
import { getLanguage } from '@/utils/auth'
import TipDialog from '@/components/TipDialog'
import { handleErrCode401 } from '@/utils/request'
import { getUserList } from '@/api/userMgt'
import { getDeviceGroupList, getDeviceList as getDevices } from '@/api/deviceMgt'

export default {
  components: {
    LangSelect,
    TipDialog
  },
  inject: ['reload'],
  data () {
    return {
      language: getLanguage(),
      dialogTitle: '',
      dialogTip: '',
      tipRequest: '',
      tipParams: {},
      selected_menu_flag: 'home',

      isSuperAdmin: false,
      isNornalAdmin: false,
      username: sessionStorage.account_alias ? sessionStorage.account_alias : sessionStorage.username
    }
  },
  computed: {
  },
  created () {
    this.selected_menu_flag = this.$route.name
    this.isSuperAdmin = sessionStorage.account_type == '1'
    this.isNornalAdmin = sessionStorage.account_type == '3'
    this.$bus.$on('refreshUserInfo', response => {
      this.username = response
    })
  },
  methods: {
    handleSetLanguage (val) {
      this.language = val
    },
    toggleMenu (flag) {
      this.selected_menu_flag = flag
      this.$router.push({ path: flag })
    },
    logoutDialog () {
      this.dialogTitle = this.$t('quitTitle')
      this.dialogTip = this.$t('quitTip')
      this.tipRequest = 'cli_logout'
      this.tipParams = {}
      this.$refs.tipDialog.show('logout')
    },
    backTip (val) {
      // if(val === 'logout'){
      //   this.$store.dispatch('FedLogOut')
      //   location.reload()
      // }
    },
    // !!test!!
    async test () {
      console.log('🧪 开始测试 401 重登机制')

      try {
        // // 第一步：先退出登录以避免单点登录冲突
        const originalToken = this.$store.getters.token
        // console.log('📤 第一步：执行 FedLogOut 避免单点登录冲突')
        // await this.$store.dispatch('FedLogOut')

        // // 等待一下确保登出完成
        // await new Promise(resolve => setTimeout(resolve, 500))

        // sessionStorage.setItem('token', originalToken)

        // // 第二步：重新初始化 WebSocket 连接
        // console.log('🔌 第二步：重新初始化 WebSocket 连接')
        // if (this.$websocket && this.$websocket.initWebSocket) {
        //   this.$websocket.initWebSocket()
        // }

        // // 等待 WebSocket 连接建立
        // await new Promise(resolve => setTimeout(resolve, 1000))

        // ===test129 start====
        // const mockData = {
        //   headers: { id: 'cli_miru_login_rsp' },
        //   ret: 129,
        //   msg: 'mock errCode129'
        // }

        // if (this.$websocket && this.$websocket.handleReceivedMessage) {
        //   this.$websocket.handleReceivedMessage(mockData)
        //   this.$message.success('??? 129???????????')
        // } else {
        //   this.$message.error('websocket ????????? 129')
        // }

        // return
        // ===test129 end====

        // 第三步：发送一个会返回 401 的 HTTP 请求
        console.log('📡 第三步：发送测试请求（模拟 401 错误）')

        // 临时修改 token 为无效值，模拟 401 错误

        this.$store.commit('SET_TOKEN', 'invalid_token_for_test')

        try {
          // 发送一个需要认证的请求，这应该会返回 401

          const result = await getDeviceGroupList()

          // 如果到这里说明请求成功了（重登成功）
          console.log('✅ 测试成功！401 重登机制工作正常')
          console.log('📊 请求结果:', result)

          this.$message.success('401 重登测试成功！')

        } catch (error) {
          // 如果重登失败，会到这里
          console.log('❌ 测试失败，重登机制未能成功处理 401 错误')
          console.error('错误详情:', error)

          // 恢复原始 token
          this.$store.commit('SET_TOKEN', originalToken)

          this.$message.error('401 重登测试失败: ' + (error.message || '未知错误'))
        }

      } catch (error) {
        console.error('❌ 测试过程中发生错误:', error)
        this.$message.error('测试过程中发生错误: ' + (error.message || '未知错误'))
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
div,
text {
  color: var(--color-neutral-100);
  font-size: var(--font-size-normal);
}
.navbar {
  height: 70px;
  width: 100%;
  min-width: 1100px;
  background: var(--color-neutral-600);
  display: flex;
  align-items: center;
  .infoBox {
    width: 100%;
    padding: 0 20px;
    height: 66px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  }
  .platInfo {
    display: flex;
    margin-right: 40px;
    .platLogo {
      width: 50px;
      height: 50px;
      margin-right: 10px;
    }
    .platNameBox {
      display: flex;
      justify-content: center;
      flex-direction: column;
      .platName {
        font-size: var(--font-size-large);
        font-weight: bolder;
      }
      .platNameTip {
        font-size: var(--font-size-small);
      }
    }
    .set-language {
      font-size: 18px;
      height: 40px;
      line-height: 40px;
    }
  }
  .menuCard {
    display: flex;
    font-size: var(--font-size-regular);
    color: #8e959d;
    .subMenu {
      display: flex;
      padding: 0 16px;
      align-items: center;
      height: 66px;
      cursor: pointer;
      &:last-child {
        margin-right: 0;
      }
      &:hover {
        background-color: var(--color-neutral-hover);
      }
    }
    .selectedSubMenu {
      font-weight: bolder;
      color: var(--color-primary);
    }
  }
  .navbar__right {
    display: flex;
    gap: 20px;
    .userName {
      color: var(--color-neutral-200);
    }
    .logOut {
      color: var(--color-neutral-200);
      &:hover {
        color: var(--color-primary);
      }
    }
  }
}
</style>
