import Cookies from 'js-cookie'

// 统一使用 localStorage 持久化跨 Tab 的账号信息
const TokenKey = 'miru_shared_sessid'
const AccountKey = 'miru_shared_account'
const PasswordKey = 'miru_shared_password'
const RoleKey = 'bsAdmin-Role'

export function getToken() {
  // 兼容老的 sessionStorage 写法，优先取新的本地持久化字段
  return localStorage.getItem(TokenKey) || sessionStorage.token
}

export function setToken(token) {
  if (token) {
    localStorage.setItem(TokenKey, token)
    sessionStorage.token = token
  }
  return token
}

export function setSharedAccount(account, password) {
  if (account) {
    localStorage.setItem(AccountKey, account)
  }
  if (password) {
    localStorage.setItem(PasswordKey, password)
  }
}

export function removeToken() {
  localStorage.removeItem(TokenKey)
  sessionStorage.removeItem('token')
}

export function removeRole() {
  return Cookies.remove(RoleKey)
}

export function getRole() {
  return Cookies.get(<PERSON><PERSON><PERSON>)
}

export function setRole(role) {
  return Cookies.set(<PERSON><PERSON><PERSON>, role)
}

export function getLanguage() {
  return Cookies.get('language')
}
