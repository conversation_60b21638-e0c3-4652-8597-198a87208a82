{"_args": [["@types/node@18.6.5", "D:\\---AProjects---\\mirruServer\\miruserver\\appWeb"]], "_development": true, "_from": "@types/node@18.6.5", "_id": "@types/node@18.6.5", "_inBundle": false, "_integrity": "sha512-Xjt5ZGUa5WusGZJ4WJPbOT8QOqp6nDynVFRKcUt32bOgvXEoc6o085WNkYTMO7ifAj2isEfQQ2cseE+wT6jsRw==", "_location": "/@types/node", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/node@18.6.5", "name": "@types/node", "escapedName": "@types%2fnode", "scope": "@types", "rawSpec": "18.6.5", "saveSpec": null, "fetchSpec": "18.6.5"}, "_requiredBy": ["/@types/glob"], "_resolved": "https://registry.npmmirror.com/@types/node/-/node-18.6.5.tgz", "_spec": "18.6.5", "_where": "D:\\---AProjects---\\mirruServer\\miruserver\\appWeb", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "Microsoft TypeScript", "url": "https://github.com/Microsoft"}, {"name": "DefinitelyTyped", "url": "https://github.com/DefinitelyTyped"}, {"name": "<PERSON>", "url": "https://github.com/jkomyno"}, {"name": "Alvis <PERSON>", "url": "https://github.com/alvis"}, {"name": "<PERSON>", "url": "https://github.com/r3nya"}, {"name": "<PERSON>", "url": "https://github.com/btoueg"}, {"name": "Chigozirim C.", "url": "https://github.com/smac89"}, {"name": "<PERSON>", "url": "https://github.com/touffy"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/DeividasBakanas"}, {"name": "<PERSON>", "url": "https://github.com/eyqs"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>-<PERSON>-CK"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/hoo29"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kjin"}, {"name": "<PERSON>", "url": "https://github.com/ajafff"}, {"name": "Lishude", "url": "https://github.com/islishude"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwiktorczyk"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mohsen1"}, {"name": "<PERSON>", "url": "https://github.com/n-e"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/galkin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/parambirs"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick"}, {"name": "<PERSON>", "url": "https://github.com/ThomasdenH"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/WilcoBakker"}, {"name": "wwwy3y3", "url": "https://github.com/wwwy3y3"}, {"name": "<PERSON>", "url": "https://github.com/samuela"}, {"name": "<PERSON>", "url": "https://github.com/kuehlein"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/bhongy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/chyzwar"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/trivikr"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/yoursunny"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/qwelias"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON>", "url": "https://github.com/victorperin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZYSzys"}, {"name": "NodeJS Contributors", "url": "https://github.com/NodeJS"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LinusU"}, {"name": "wafuwafu13", "url": "https://github.com/wafuwafu13"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}], "dependencies": {}, "description": "TypeScript definitions for Node.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node", "license": "MIT", "main": "", "name": "@types/node", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node"}, "scripts": {}, "typeScriptVersion": "4.0", "types": "index.d.ts", "typesPublisherContentHash": "4d8149f864b93846526dbd58e91ce534c655f81b4e3a0c22cd7d03534790d99d", "version": "18.6.5"}