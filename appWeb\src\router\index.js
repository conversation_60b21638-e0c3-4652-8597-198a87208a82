import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/views/layout/Layout";

export const constantRouterMap = [
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true
  },
  {
    path: "/404",
    component: () => import("@/views/errorPage/404"),
    hidden: true
  },
  {
    path: "/401",
    component: () => import("@/views/errorPage/401"),
    hidden: true
  }
];

export default new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
});

export const asyncRouterMap = [
  {
    path: "/home",
    component: Layout,
    children: [
      {
        path: "",
        component: () => import("@/views/video"),
        name: "home"
      }
    ],
    hidden: true
  },
  {
    //   path: '/p2p',
    //   component: Layout,
    //   children: [{
    //     path: '',
    //     component: () =>
    //       import('@/views/p2pVideo'),
    //     name: 'p2p'
    //   }],
    // },{
    path: "/device",
    component: Layout,
    children: [
      {
        path: "",
        component: () => import("@/views/deviceMgt/index"),
        name: "device"
      }
    ]
  },
  {
    path: "/user",
    component: Layout,
    children: [
      {
        path: "",
        component: () => import("@/views/userMgt/index"),
        name: "user"
      }
    ]
  },
  {
    path: "/system",
    component: Layout,
    children: [
      {
        path: "",
        component: () => import("@/views/systemSetting/index"),
        name: "system"
      }
    ]
  },
  {
    path: "/video_management",
    component: Layout,
    children: [
      {
        path: "",
        component: () => import("@/views/videoMgt/index"),
        name: "video_management"
      }
    ]
  },
  {
    path: "/logs",
    component: Layout,
    children: [
      {
        path: "",
        component: () => import("@/views/operationLogs/index"),
        name: "logs"
      }
    ]
  },
  { path: "*", redirect: "/login", hidden: true }
];
