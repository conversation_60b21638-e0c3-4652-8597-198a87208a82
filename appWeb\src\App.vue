<template>
  <div id="app">
    <router-view v-if="isRouterAlive" />
  </div>
</template>

<script>
// import { decryption } from '@/utils/jiami'
// import Cookies from 'js-cookie'
import Vue from 'vue'
import tabManager from '@/utils/tabManager'
Vue.prototype.$bus = new Vue()
export default {
  name: 'App',
  provide () {
    return {
      reload: this.reload
    }
  },
  data () {
    return {
      isRouterAlive: true
    }
  },
  created () {
    // // 刷新界面websocket断开重连
    // if (sessionStorage.username && sessionStorage.username !== '') {
    //   this.$websocket.initWebSocket(JSON.parse(decryption(Cookies.get('loginData'))))
    // }

    // 注册全局登出监听器
    this.setupLogoutListener()

    console.log('created')
    // 检查当前是否在登录页
    if (this.$route && this.$route.path == '/login') {

    }else {
      // 不在登录页的情况下，检测当前的sessionStorage是否包含token，不包含的话就退回登录页
      if (!sessionStorage.token) {
        this.$router.replace('/login')
      }

    }
  },
  methods: {
    reload () {
      this.isRouterAlive = false
      this.$nextTick(function () {
        this.isRouterAlive = true
      })
    },
    setupLogoutListener () {
      // 注册登出回调，当其他 Tab 登出时自动执行
      tabManager.onLogout(() => {
        console.log('Received logout event from another tab')
        // 检查当前是否在登录页，如果不在则跳转
        if (this.$route && this.$route.path !== '/login') {
          console.log('Redirecting to login page due to logout in another tab')
          // 执行登出操作，不触发登出事件（避免循环）
          this.$websocket.closeWebsocket()
          this.$router.replace('/login')
        }
      })
    }
  }
}
</script>
<style lang="scss">
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.2);
  -webkit-border-radius: 6px;
}
::-webkit-scrollbar-thumb:vertical {
  height: 15px;
  background-color: rgba(125, 125, 125, 0.7);
  -webkit-border-radius: 6px;
}
::-webkit-scrollbar-thumb:horizontal {
  width: 15px;
  background-color: rgba(125, 125, 125, 0.7);
  -webkit-border-radius: 6px;
}
</style>
