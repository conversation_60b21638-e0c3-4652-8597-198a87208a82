<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    append-to-body
    :before-close="handleCancelIcon"
    @close="hideDialog">
    <section>
      <div class="content text-center marBtm20">
        {{ tip }}
      </div>
    </section>
    <span slot="footer" class="dialog-footer">
      <el-button :loading="loading" class="tipSubmitBtn noFlex" type="primary" @click="submit" >{{ one_btn_text }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'OneBtnTipDialog',
  props: {
    request: {
      type: Function,
      default: undefined
    },
    params: {
      type: Object,
      default() {
        return {}
      }
    },
    title: {
      type: String,
      default: ''
    },
    tip: { // 提示语句
      type: String,
      default: ''
    },
    one_btn_text: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      dialogVisible: false,
      name: '',
      cid: '',
      loading: false
    }
  },
  methods: {
    show(name, cid) {
      this.dialogVisible = true
      this.name = name
      this.cid = cid
      console.log(this.name, this.cid)
    },
    hideDialog() {
      this.dialogVisible = false
    },
    submit() {
      this.loading = true
      if(!this.request || typeof this.request !== 'function'){
          this.$emit('handleTip', this.name, this.cid)
          this.loading = false
          this.hideDialog()
      }else {
        this.request(this.params).then(response => {
          this.$emit('handleTip', this.name, this.cid, response)
          this.loading = false
          this.hideDialog()
        }).catch(() => {
          this.loading = false
        })
      }
    },
    handleCancelIcon(done) {
      this.$emit('handleCancel',this.name, this.cid)
      done()
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  /deep/ .el-dialog{
    width: 400px;
  }
  .content {
    font-weight: bold;
    color: var(--color-white);
    word-break: normal;
  }
  .noFlex {
    flex: none;
  }
</style>
